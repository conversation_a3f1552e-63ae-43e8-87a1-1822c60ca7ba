﻿using FS.Keycloak.RestApiClient.Client;
using FS.Keycloak.RestApiClient.Model;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Keycloak
{
    public interface IKeycloakService
    {
        Task<List<UserRepresentation>> GetUsers();
        Task<ApiResponse<object>> <PERSON><PERSON>User(UserRepresentation user, CancellationToken cancellationToken = default(CancellationToken));
        Task<ApiResponse<object>> DeleteUser(string userId, CancellationToken cancellationToken = default(CancellationToken));
        Task<bool> IsUserByUsernameExists(string username);
        Task<string> GetUserIdByUsername(string username);
        Task<UserRepresentation?> GetKeycloackUserByUsernameOrNullAsync(string username);
        Task<UserRepresentation> GetUserById(string userId, CancellationToken cancellationToken = default(CancellationToken));
        Task<ApiResponse<object>> UpdateUser(UserRepresentation user, CancellationToken cancellationToken = default(CancellationToken));
        Task<ApiResponse<object>> SetEmailVerified(string userId, bool emailVerified = true, CancellationToken cancellationToken = default(CancellationToken));
        Task<ApiResponse<object>> ResetEmailUserPasswordByUserId(string userId, CancellationToken cancellationToken = default(CancellationToken));
        Task<ApiResponse<object>> ResetUserPasswordByUserId(string userId, string newPassword, bool forceChangePasswordNextSignIn = false, CancellationToken cancellationToken = default(CancellationToken));

    }
}
