﻿using Newtonsoft.Json;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDAQuerySentForOrder
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Query Sent for Order\"";

        [JsonProperty("Query Sent for Order")]
        public GfsEDAQuerySentForOrderPayload QuerySentForOrder { get; set; } = new();
    }

    public class GfsEDAQuerySentForOrderPayload
    {
        public DateTime? ApplicableFrom { get; set; }
        public DateTime? ApplicableTo { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        public int MessageStatus { get; set; }
        public int MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public bool Read { get; set; }

        // Specific to Query Sent for Order
        public string Subject { get; set; } = default!;
        public string Text { get; set; } = default!;

        public int QueryMessageType { get; set; }
        public List<GfsQueryField> QueryFields { get; set; } = new();

        public string AttachedUrl { get; set; }
        public bool WaitingForResponse { get; set; }

        public int? RelatedMessageId { get; set; }
    }

    public class GfsQueryField
    {
        public enum QueryFieldNameIds
        {
            ItemPrice = 1,
            DeliveryDate = 10,
            TotalOrderValue = 47,
            ExtraDeliveryFee = 48
        }
        public int Field { get; set; } = default!;
        public int ItemNumber { get; set; } = default!;
        public string Value { get; set; } = default!;
    }
}
