﻿using Newtonsoft.Json;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDASentBackchargedMessage
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Sent Backcharged Message\"";

        [JsonProperty("Sent Backcharged Message")]
        public GfsEDASentBackchargedMessagePayload SentBackchargedMessage { get; set; }
    }
    public class GfsEDASentBackchargedMessagePayload
    {
        public int BackchargeId { get; set; }
        public double Amount { get; set; }
        public int BackchargeType { get; set; }
        public int BackchargeState { get; set; }
        public string Reason { get; set; }
        public object ClearingPeriod { get; set; }
        public string Evidence { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        public int MessageStatus { get; set; }
        public int MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public object ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; }
        public int Priority { get; set; }
        public object RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }
}
