﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    //internal class GroupEDAFloristAssignedMessage
    //{
    //}

    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDAFloristAssignedMessage : BaseMessage<GroupEDAFloristAssignedMessagePayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDAFloristAssignedMessage()
                {
                    this.Payload = new GroupEDAFloristAssignedMessagePayload();
                }

                public static implicit operator GroupEDAFloristAssignedMessage(GfsEDAFloristAssignedMessage v)
                {
                    GroupEDAFloristAssignedMessage msg = new();
                    msg.Payload = new();
                    msg.Payload.AssignmentDate = v.SentFloristAssignedMessage.AssignmentDate;
                    msg.Payload.Id = v.SentFloristAssignedMessage.Id;
                    msg.Payload.GFSgateNumber = v.SentFloristAssignedMessage.GFSgateNumber;
                    msg.Payload.MessageStatus = v.SentFloristAssignedMessage.MessageStatus;
                    msg.Payload.MessageType = v.SentFloristAssignedMessage.MessageType;
                    msg.Payload.CreatedDate = v.SentFloristAssignedMessage.CreatedDate;
                    msg.Payload.ModifiedDate = v.SentFloristAssignedMessage.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.SentFloristAssignedMessage.ConfirmedDate;
                    msg.Payload.FromUnitID = v.SentFloristAssignedMessage.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.SentFloristAssignedMessage.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.SentFloristAssignedMessage.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.SentFloristAssignedMessage.ToUnitMessageID;
                    msg.Payload.Operator = v.SentFloristAssignedMessage.Operator;
                    msg.Payload.Priority = v.SentFloristAssignedMessage.Priority;
                    msg.Payload.RelatedMessageId = v.SentFloristAssignedMessage.RelatedMessageId;
                    msg.Payload.Read = v.SentFloristAssignedMessage.Read;
                    return msg;
                }
            }
        }
    }


    public class GroupEDAFloristAssignedMessagePayload : LegacyPayload, IEquatable<GroupEDAFloristAssignedMessagePayload>
    {
        public DateTime AssignmentDate { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDAFloristAssignedMessagePayload parameter)
        {
            return (Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType == parameter.MessageType &&
                    CreatedDate == parameter.CreatedDate &&
                    ModifiedDate == parameter.ModifiedDate &&
                    ConfirmedDate == parameter.ConfirmedDate &&
                    FromUnitID == parameter.FromUnitID &&
                    FromUnitMessageID == parameter.FromUnitMessageID &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDAFloristAssignedMessagePayload);
        }

        public override int GetHashCode() => new
        {
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read
        }.GetHashCode();
    }
}
