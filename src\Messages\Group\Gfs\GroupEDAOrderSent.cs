﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDAOrderSent : BaseMessage<GroupEDAOrderSentPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDAOrderSent()
                {
                    this.Payload = new GroupEDAOrderSentPayload();
                }

                public static implicit operator GroupEDAOrderSent(GfsEDAOrderSent v)
                {
                    GroupEDAOrderSent msg = new();
                    msg.Payload = new();
                    msg.Payload.DeliveredDate = v.OrderSent.DeliveredDate;
                    msg.Payload.DeliveryDate = v.OrderSent.DeliveryDate;

                    msg.Payload.SendingUnitOrderNumber = v.OrderSent.SendingUnitOrderNumber;
                    msg.Payload.ExecutingUnitOrderNumber = v.OrderSent.ExecutingUnitOrderNumber;
                    msg.Payload.OrderType = v.OrderSent.OrderType;
                    msg.Payload.OrderState = v.OrderSent.OrderState;
                    msg.Payload.Occasion = v.OrderSent.Occasion;
                    msg.Payload.SenderCompany = v.OrderSent.SenderCompany;
                    msg.Payload.SenderName = v.OrderSent.SenderName;
                    msg.Payload.SenderAddress = v.OrderSent.SenderAddress;
                    msg.Payload.SenderCity = v.OrderSent.SenderCity;
                    msg.Payload.SenderRegion = v.OrderSent.SenderRegion;
                    msg.Payload.SenderPostalCode = v.OrderSent.SenderPostalCode;
                    msg.Payload.SenderCountryCode = v.OrderSent.SenderCountryCode;
                    msg.Payload.SenderPhoneHome = v.OrderSent.SenderPhoneHome;
                    msg.Payload.SenderPhoneWork = v.OrderSent.SenderPhoneWork;
                    msg.Payload.SenderPhoneMobile = v.OrderSent.SenderPhoneMobile;
                    msg.Payload.SenderFax = v.OrderSent.SenderFax;
                    msg.Payload.SenderEmail1 = v.OrderSent.SenderEmail1;
                    msg.Payload.SenderEmail2 = v.OrderSent.SenderEmail2;
                    msg.Payload.RecipientTitle = v.OrderSent.RecipientTitle;
                    msg.Payload.RecipientName = v.OrderSent.RecipientName;
                    msg.Payload.RecipientAddress = v.OrderSent.RecipientAddress;
                    msg.Payload.RecipientCity = v.OrderSent.RecipientCity;
                    msg.Payload.RecipientRegion = v.OrderSent.RecipientRegion;
                    msg.Payload.RecipientPostalCode = v.OrderSent.RecipientPostalCode;
                    msg.Payload.RecipientCountryCode = v.OrderSent.RecipientCountryCode;
                    msg.Payload.RecipientPhoneHome = v.OrderSent.RecipientPhoneHome;
                    msg.Payload.RecipientPhoneWork = v.OrderSent.RecipientPhoneWork;
                    msg.Payload.RecipientPhoneMobile = v.OrderSent.RecipientPhoneMobile;
                    msg.Payload.RecipientFax = v.OrderSent.RecipientFax;
                    msg.Payload.RecipientEmail = v.OrderSent.RecipientEmail;
                    msg.Payload.TotalOrderValue = v.OrderSent.TotalOrderValue;
                    msg.Payload.OrderInstructions = v.OrderSent.OrderInstructions;
                    msg.Payload.SendingMemberCode = v.OrderSent.SendingMemberCode;
                    msg.Payload.SendingLocation = v.OrderSent.SendingLocation;
                    msg.Payload.SendingName = v.OrderSent.SendingName;
                    msg.Payload.SendingMemberOrderNumber = v.OrderSent.SendingMemberOrderNumber;
                    msg.Payload.FillingMemberCode = v.OrderSent.FillingMemberCode;
                    msg.Payload.FillingName = v.OrderSent.FillingName;
                    msg.Payload.FillingLocation = v.OrderSent.FillingLocation;
                    msg.Payload.ClearingPeriod = v.OrderSent.ClearingPeriod;
                    msg.Payload.ExtraDeliveryFee = v.OrderSent.ExtraDeliveryFee;
                    msg.Payload.DeliveredDate = v.OrderSent.DeliveredDate;
                    msg.Payload.Id = v.OrderSent.Id;
                    msg.Payload.GFSgateNumber = v.OrderSent.GFSgateNumber;
                    msg.Payload.MessageStatus = v.OrderSent.MessageStatus;
                    msg.Payload.MessageType = v.OrderSent.MessageType;
                    msg.Payload.CreatedDate = v.OrderSent.CreatedDate;
                    msg.Payload.ModifiedDate = v.OrderSent.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.OrderSent.ConfirmedDate;
                    msg.Payload.FromUnitID = v.OrderSent.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.OrderSent.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.OrderSent.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.OrderSent.ToUnitMessageID;
                    msg.Payload.Operator = v.OrderSent.Operator;
                    msg.Payload.Priority = v.OrderSent.Priority;
                    msg.Payload.RelatedMessageId = v.OrderSent.RelatedMessageId;
                    msg.Payload.Read = v.OrderSent.Read;

                    if(v.OrderSent.OrderItems != null)
                    {
                        foreach(var gfsItem in v.OrderSent.OrderItems)
                        {
                            GroupOrderItem item = new();
                            item.Price = gfsItem.Price;
                            item.Quantity = gfsItem.Quantity;
                            item.IntercatCode = gfsItem.IntercatCode;
                            item.SecondChoice = gfsItem.SecondChoice;
                            item.SKU = gfsItem.SKU;
                            item.CardMessage = gfsItem.CardMessage;
                            item.Description = gfsItem.Description;
                            msg.Payload.OrderItems.Add(item);
                        }
                    }
                    return msg;
                }
            }
        }
    }



    public class GroupEDAOrderSentPayload : LegacyPayload, IEquatable<GroupEDAOrderSentPayload>
    {
        public DateTime DeliveryDate { get; set; }
        public string SendingUnitOrderNumber { get; set; } = default!;
        public string ExecutingUnitOrderNumber { get; set; } = default!;
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOrderType OrderType { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOrderState OrderState { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOccasion Occasion { get; set; }
        public string SenderCompany { get; set; } = default!;
        public string SenderName { get; set; } = default!;
        public string SenderAddress { get; set; } = default!;
        public string SenderCity { get; set; } = default!;
        public string SenderRegion { get; set; } = default!;
        public string SenderPostalCode { get; set; } = default!;
        public string SenderCountryCode { get; set; } = default!;
        public string SenderPhoneHome { get; set; } = default!;
        public string SenderPhoneWork { get; set; } = default!;
        public string SenderPhoneMobile { get; set; } = default!;
        public string SenderFax { get; set; } = default!;
        public string SenderEmail1 { get; set; } = default!;
        public string SenderEmail2 { get; set; } = default!;
        public string RecipientTitle { get; set; } = default!;
        public string RecipientName { get; set; } = default!;
        public string RecipientAddress { get; set; } = default!;
        public string RecipientCity { get; set; } = default!;
        public string RecipientRegion { get; set; } = default!;
        public string RecipientPostalCode { get; set; } = default!;
        public string RecipientCountryCode { get; set; } = default!;
        public string RecipientPhoneHome { get; set; } = default!;
        public string RecipientPhoneWork { get; set; } = default!;
        public string RecipientPhoneMobile { get; set; } = default!;
        public string RecipientFax { get; set; } = default!;
        public string RecipientEmail { get; set; } = default!;
        public double TotalOrderValue { get; set; }
        public string OrderInstructions { get; set; } = default!;
        public string SendingMemberCode { get; set; } = default!;
        public string SendingLocation { get; set; } = default!;
        public string SendingName { get; set; } = default!;
        public string SendingMemberOrderNumber { get; set; } = default!;
        public string FillingMemberCode { get; set; } = default!;
        public string FillingName { get; set; } = default!;
        public string FillingLocation { get; set; } = default!;
        public object ClearingPeriod { get; set; } = default!;
        public int ExtraDeliveryFee { get; set; }
        public DateTime? DeliveredDate { get; set; }
        public List<GroupOrderItem> OrderItems { get; set; } = new();
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDAOrderSentPayload parameter)
        {
            return (DeliveryDate.Equals(parameter.DeliveryDate) &&
                    SendingUnitOrderNumber.Equals(parameter.SendingUnitOrderNumber) &&
                    ExecutingUnitOrderNumber.Equals(parameter.ExecutingUnitOrderNumber) &&
                    OrderType.Equals(parameter.OrderType) &&
                    OrderState.Equals(parameter.OrderState) &&
                    Occasion.Equals(parameter.Occasion) &&
                    SenderCompany.Equals(parameter.SenderCompany) &&
                    SenderName.Equals(parameter.SenderName) &&
                    SenderAddress.Equals(parameter.SenderAddress) &&
                    SenderCity.Equals(parameter.SenderCity) &&
                    SenderRegion.Equals(parameter.SenderRegion) &&
                    SenderPostalCode.Equals(parameter.SenderPostalCode) &&
                    SenderCountryCode.Equals(parameter.SenderCountryCode) &&
                    SenderPhoneHome.Equals(parameter.SenderPhoneHome) &&
                    SenderPhoneWork.Equals(parameter.SenderPhoneWork) &&
                    SenderPhoneMobile.Equals(parameter.SenderPhoneMobile) &&
                    SenderFax.Equals(parameter.SenderFax) &&
                    SenderEmail1.Equals(parameter.SenderEmail1) &&
                    SenderEmail2.Equals(parameter.SenderEmail2) &&
                    RecipientTitle.Equals(parameter.RecipientTitle) &&
                    RecipientName.Equals(parameter.RecipientName) &&
                    RecipientAddress.Equals(parameter.RecipientAddress) &&
                    RecipientCity.Equals(parameter.RecipientCity) &&
                    RecipientRegion.Equals(parameter.RecipientRegion) &&
                    RecipientPostalCode.Equals(parameter.RecipientPostalCode) &&
                    RecipientCountryCode.Equals(parameter.RecipientCountryCode) &&
                    RecipientPhoneHome.Equals(parameter.RecipientPhoneHome) &&
                    RecipientPhoneWork.Equals(parameter.RecipientPhoneWork) &&
                    RecipientPhoneMobile.Equals(parameter.RecipientPhoneMobile) &&
                    RecipientFax.Equals(parameter.RecipientFax) &&
                    RecipientEmail.Equals(parameter.RecipientEmail) &&
                    TotalOrderValue.Equals(parameter.TotalOrderValue) &&
                    OrderInstructions.Equals(parameter.OrderInstructions) &&
                    SendingMemberCode.Equals(parameter.SendingMemberCode) &&
                    SendingLocation.Equals(parameter.SendingLocation) &&
                    SendingName.Equals(parameter.SendingName) &&
                    SendingMemberOrderNumber.Equals(parameter.SendingMemberOrderNumber) &&
                    FillingMemberCode.Equals(parameter.FillingMemberCode) &&
                    FillingName.Equals(parameter.FillingName) &&
                    FillingLocation.Equals(parameter.FillingLocation) &&
                    ClearingPeriod.Equals(parameter.ClearingPeriod) &&
                    ExtraDeliveryFee.Equals(parameter.ExtraDeliveryFee) &&
                    DeliveredDate.Equals(parameter.DeliveredDate) &&
                    Id.Equals(parameter.Id) &&
                    GFSgateNumber.Equals(parameter.GFSgateNumber) &&
                    MessageStatus.Equals(parameter.MessageStatus) &&
                    MessageType.Equals(parameter.MessageType) &&
                    CreatedDate.Equals(parameter.CreatedDate) &&
                    ModifiedDate.Equals(parameter.ModifiedDate) &&
                    ConfirmedDate.Equals(parameter.ConfirmedDate) &&
                    FromUnitID.Equals(parameter.FromUnitID) &&
                    FromUnitMessageID.Equals(parameter.FromUnitMessageID) &&
                    ToUnitID.Equals(parameter.ToUnitID) &&
                    ToUnitMessageID.Equals(parameter.ToUnitMessageID) &&
                    Operator.Equals(parameter.Operator) &&
                    Priority.Equals(parameter.Priority) &&
                    RelatedMessageId.Equals(parameter.RelatedMessageId) &&
                    Read.Equals(parameter.Read) &&
                    OrderItems.Equals(parameter.OrderItems)
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDAOrderSentPayload);
        }

        public override int GetHashCode() => new
        {
            DeliveryDate,
            SendingUnitOrderNumber,
            ExecutingUnitOrderNumber,
            OrderType,
            OrderState,
            Occasion,
            SenderCompany,
            SenderName,
            SenderAddress,
            SenderCity,
            SenderRegion,
            SenderPostalCode,
            SenderCountryCode,
            SenderPhoneHome,
            SenderPhoneWork,
            SenderPhoneMobile,
            SenderFax,
            SenderEmail1,
            SenderEmail2,
            RecipientTitle,
            RecipientName,
            RecipientAddress,
            RecipientCity,
            RecipientRegion,
            RecipientPostalCode,
            RecipientCountryCode,
            RecipientPhoneHome,
            RecipientPhoneWork,
            RecipientPhoneMobile,
            RecipientFax,
            RecipientEmail,
            TotalOrderValue,
            OrderInstructions,
            SendingMemberCode,
            SendingLocation,
            SendingName,
            SendingMemberOrderNumber,
            FillingMemberCode,
            FillingName,
            FillingLocation,
            ClearingPeriod,
            ExtraDeliveryFee,
            DeliveredDate,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read,
            OrderItems
        }.GetHashCode();
    }

    public class GroupOrderItem
    {
        public double Price { get; set; }
        public int Quantity { get; set; }
        public string IntercatCode { get; set; } = default!;
        public string SecondChoice { get; set; } = default!;
        public string SKU { get; set; } = default!;
        public string CardMessage { get; set; } = default!;
        public string Description { get; set; } = default!;


        public bool Equals(GroupOrderItem parameter)
        {
            return (Price == parameter.Price &&
                    Quantity == parameter.Quantity &&
                    IntercatCode == parameter.IntercatCode &&
                    SecondChoice == parameter.SecondChoice &&
                    SKU == parameter.SKU &&
                    CardMessage == parameter.CardMessage &&
                    Description == parameter.Description
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupOrderItem);
        }
        public override int GetHashCode() => new
        {
            Price,
            Quantity,
            IntercatCode,
            SecondChoice,
            SKU,
            CardMessage,
            Description,

        }.GetHashCode();
    }

}
