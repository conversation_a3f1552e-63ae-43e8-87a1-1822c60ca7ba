using commercetools.Sdk.Api.Models.CustomerGroups;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Stores;
using FS.Keycloak.RestApiClient.Model;
using IT.Microservices.AuthenticationApi.Login;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using System.Text.Json.Nodes;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IT.Microservices.AuthenticationApi.CreateUser;

public interface ICreateUserUseCase
{
    public Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req);
    public Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req);
}

// Context records for functional pipeline
public record CreateUserContext(
    CreateUserRequest Request,
    UserRepresentation? KeycloakUser = null,
    ICustomer? Customer = null
);

public record ValidateUserAccountContext(
    ValidateUserAccountRequest Request,
    ICustomer? Customer = null,
    UserRepresentation? KeycloakUser = null
);

// Extension methods for Result handling
public static class CreateUserResultExtensions
{
    public static Result<T, TError> LogOnFailure<T, TError>(this Result<T, TError> result, Microsoft.Extensions.Logging.ILogger logger, string message)
    {
        if (result.IsFailure)
            logger.LogError("{Message}: {Error}", message, result.Error);
        return result;
    }

    public static async Task<Result<T, TError>> LogOnFailureAsync<T, TError>(this Task<Result<T, TError>> resultTask, Microsoft.Extensions.Logging.ILogger logger, string message)
    {
        var result = await resultTask;
        return result.LogOnFailure(logger, message);
    }
}

public class CreateUserUseCase(ILogger<CreateUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : ICreateUserUseCase
{
    private readonly string? _storeKey = conf?.GetSection("Client:StoreProjectionKey").Value;

    // Helper methods for error creation
    private static CreateUserFailedResponse CreateError(string error, string description, bool isException = false) =>
        new(error, description, isException);

    private static ValidateUserAccountFailedResponse CreateValidationError(string error, string description, bool isException = false) =>
        new(error, description, isException);

    private static string GenerateRedirectUrl(string baseUrl, string token) =>
        $"{baseUrl}?code={token}";

    // Pipeline methods for CreateUser flow
    private Result<CreateUserContext, CreateUserFailedResponse> ValidateCreateUserRequest(CreateUserContext context)
    {
        var req = context.Request;
        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.Password) || string.IsNullOrWhiteSpace(req.RedirectUrl))
            return CreateError("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        var normalizedRequest = req with { Email = req.Email.Trim().ToLower() };
        return Result.Success<CreateUserContext, CreateUserFailedResponse>(context with { Request = normalizedRequest });
    }

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> CheckKeycloakUserNotExists(CreateUserContext context)
    {
        try
        {
            var user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(context.Request.Email);
            if (user is not null)
            {
                var errorContent = $"User with email {context.Request.Email} already exists in Keycloak";
                logger.LogError(errorContent);
                return CreateError("UserAlreadyExist", errorContent);
            }
            return Result.Success<CreateUserContext, CreateUserFailedResponse>(context);
        }
        catch (Exception e)
        {
            return CreateError(e.GetType().ToString(), $"Exception occurred: {e}", true);
        }
    }

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> CreateKeycloakUser(CreateUserContext context)
    {
        var req = context.Request;
        var keycloakRes = await keycloakService.CreateUser(new UserRepresentation
        {
            Enabled = true,
            EmailVerified = false,
            Username = req.Email,
            Email = req.Email,
            Credentials = [new CredentialRepresentation { Type = "password", Temporary = false, Value = req.Password }],
            RequiredActions = ["VERIFY_EMAIL"]
        });

        if (!keycloakRes.StatusCode.IsSuccessStatusCode())
        {
            var errorContent = $"Error while creating the user into Keycloak for email : {req.Email} with the error : {keycloakRes.ErrorText}";
            logger.LogError(errorContent);
            return CreateError("CreationInKeycloakFailed", errorContent);
        }

        return Result.Success<CreateUserContext, CreateUserFailedResponse>(context);
    }

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> GetOrCreateCustomerInCT(CreateUserContext context)
    {
        var req = context.Request;
        var ctUserExists = await custService.GetByEmail(req.Email);

        if (ctUserExists is null)
        {
            var customer = await CreateNewCustomer(req);
            return customer.IsSuccess
                ? Result.Success<CreateUserContext, CreateUserFailedResponse>(context with { Customer = customer.Value })
                : customer.Error;
        }
        else
        {
            var customer = await UpdateExistingCustomer(ctUserExists, req);
            return customer.IsSuccess
                ? Result.Success<CreateUserContext, CreateUserFailedResponse>(context with { Customer = customer.Value })
                : customer.Error;
        }
    }

    private async Task<Result<ICustomer, CreateUserFailedResponse>> CreateNewCustomer(CreateUserRequest req)
    {
        var ctUserDraft = new CustomerDraft
        {
            Email = req.Email,
            Addresses = [],
            IsEmailVerified = false,
            AuthenticationMode = IAuthenticationMode.ExternalAuth,
            Stores = [new StoreResourceIdentifier() { Key = _storeKey }],
            CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey },
            CustomerGroupAssignments = [new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } }],
        };

        var ctRes = await custService.CreateCustomer(ctUserDraft);
        if (ctRes is null)
        {
            var errorContent = $"Error while creating the user with payload draft : {ctUserDraft.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
            logger.LogError(errorContent);
            return CreateError("CreationInCTFailed", errorContent);
        }

        return Result.Success<ICustomer, CreateUserFailedResponse>(ctRes);
    }

    private async Task<Result<ICustomer, CreateUserFailedResponse>> UpdateExistingCustomer(ICustomer ctUserExists, CreateUserRequest req)
    {
        logger.LogWarning("User with data from CT : {Payload} with email {Email} already exists in CT, update process to change customerGroups",
            ctUserExists.Serialize(Serializer.SerializerType.CommerceTools, serializerService), req.Email);

        var customerUpdateActions = DetermineCustomerUpdateActions(ctUserExists);

        if (customerUpdateActions.Count == 0)
        {
            logger.LogInformation("User with email {Email} already exists in CT and no update actions are needed", req.Email);
            return Result.Success<ICustomer, CreateUserFailedResponse>(ctUserExists);
        }

        var updatedCust = await custService.UpdateCustomer(ctUserExists, customerUpdateActions);
        if (updatedCust == null)
        {
            var errorContent = $"Error while updating the user : {ctUserExists.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} with payload actions update : {customerUpdateActions.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
            logger.LogError(errorContent);
            return CreateError("UpdateInCTFailed", errorContent);
        }

        return Result.Success<ICustomer, CreateUserFailedResponse>(updatedCust);
    }

    private List<ICustomerUpdateAction> DetermineCustomerUpdateActions(ICustomer ctUserExists)
    {
        var customerUpdateActions = new List<ICustomerUpdateAction>();

        // if no stores are assigned to the user, we add the store of the current country
        if (ctUserExists.Stores is null || ctUserExists.Stores.Count == 0)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has no stores, adding store {StoreKey}", ctUserExists.Email, _storeKey);
            customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
        }
        else if (ctUserExists.Stores.All(s => s.Key != _storeKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but does not have store {StoreKey} current stores : {Stores} , adding it",
                ctUserExists.Email, _storeKey, ctUserExists.Stores?.Serialize(Serializer.SerializerType.CommerceTools, serializerService));
            customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
        }

        // Customer group management
        AddCustomerGroupUpdateActions(ctUserExists, customerUpdateActions);

        return customerUpdateActions;
    }

    private void AddCustomerGroupUpdateActions(ICustomer ctUserExists, List<ICustomerUpdateAction> customerUpdateActions)
    {
        // if the user is not in the registered customer group, we add it
        if (ctUserExists.CustomerGroupAssignments is null ||
            ctUserExists.CustomerGroupAssignments.All(cga => cga.CustomerGroup?.Obj?.Key != CustomerCommonValues.RegisteredCustomerGroupKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but does not have customer group 'registered', adding it", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerAddCustomerGroupAssignmentAction
            {
                CustomerGroupAssignment = new CustomerGroupAssignmentDraft()
                {
                    CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
                }
            });
        }

        // if the user is in the anonymous customer group, we remove it
        if (ctUserExists.CustomerGroupAssignments is not null &&
            ctUserExists.CustomerGroupAssignments.Any(cga => cga.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but has customer group 'anonymous', removing it", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerRemoveCustomerGroupAssignmentAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.AnonymousCustomerGroupKey }
            });
        }

        // Handle main customer group
        if (ctUserExists.CustomerGroup == null)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has no customer group, set it with 'registered' group", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerSetCustomerGroupAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
            });
        }
        else if (ctUserExists.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has customer group '{CustomerGroup}', replacing it with 'registered'",
                ctUserExists.Email, ctUserExists.CustomerGroup?.Obj?.Key);
            customerUpdateActions.Add(new CustomerSetCustomerGroupAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
            });
        }
    }

    private async Task<Result<CreateUserResponse, CreateUserFailedResponse>> GenerateEmailTokenAndRedirectUrl(CreateUserContext context)
    {
        var customer = context.Customer!;
        var redirectUrl = context.Request.RedirectUrl;

        var emailTokenResult = await custService.CreateEmailToken(customer);
        if (emailTokenResult == null || string.IsNullOrEmpty(emailTokenResult.Value))
        {
            var errorContent = $"Error while generating email token for customer : {customer.Id} with email : {context.Request.Email}";
            logger.LogError(errorContent);
            return CreateError("EmailTokenGenerationFailed", errorContent);
        }

        var finalRedirectUrl = GenerateRedirectUrl(redirectUrl, emailTokenResult.Value);
        logger.LogInformation("Email token generated for customer: {CustomerId}, redirect URL: {RedirectUrl}", customer.Id, finalRedirectUrl);

        return Result.Success<CreateUserResponse, CreateUserFailedResponse>(new CreateUserResponse(true, finalRedirectUrl));
    }

    public async Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req) =>
        await Result.Success(new CreateUserContext(req))
            .Bind(ValidateCreateUserRequest)
            .Bind(CheckKeycloakUserNotExists)
            .Bind(CreateKeycloakUser)
            .Bind(GetOrCreateCustomerInCT)
            .Bind(GenerateEmailTokenAndRedirectUrl);

    // Pipeline methods for ValidateUserAccount flow
    private static Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> ValidateAccountRequest(ValidateUserAccountContext context)
    {
        if (string.IsNullOrWhiteSpace(context.Request.EmailToken))
            return Task.FromResult(Result.Failure<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(CreateValidationError("InvalidRequestData", "Email token is required")));

        return Task.FromResult(Result.Success<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(context));
    }

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> GetCustomerByEmailToken(ValidateUserAccountContext context)
    {
        try
        {
            var customer = await custService.GetCustomerByEmailToken(context.Request.EmailToken);
            if (customer == null)
            {
                logger.LogWarning("Get User from Email token failed for token: {EmailToken}", context.Request.EmailToken);
                return CreateValidationError("GetUserInCTFromEmailTokenFailed", "Impossible to find the user in CommerceTools with this EmailToken");
            }

            return Result.Success<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(context with { Customer = customer });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting customer by email token: {EmailToken}", context.Request.EmailToken);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }
    }

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> GetKeycloakUser(ValidateUserAccountContext context)
    {
        try
        {
            var keycloakUser = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(context.Customer!.Email);
            if (keycloakUser == null)
            {
                logger.LogError("Keycloak user not found for email: {Email}", context.Customer.Email);
                return CreateValidationError("UserNotFoundInKeycloak", "User not found in authentication system");
            }

            return Result.Success<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(context with { KeycloakUser = keycloakUser });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting Keycloak user for email: {Email}", context.Customer!.Email);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }
    }

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> VerifyEmailToken(ValidateUserAccountContext context)
    {
        try
        {
            var customerVerifiedInCt = await custService.VerifyEmailToken(context.Request.EmailToken);
            if (customerVerifiedInCt == null)
            {
                logger.LogWarning("Email token verification failed for token: {EmailToken}", context.Request.EmailToken);
                return CreateValidationError("InvalidToken", "The email verification token is invalid or has expired");
            }

            logger.LogInformation("Email token verified successfully for customer: {CustomerId}", context.Customer!.Id);
            return Result.Success<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error verifying email token: {EmailToken}", context.Request.EmailToken);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }
    }

    private async Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> UpdateKeycloakEmailVerification(ValidateUserAccountContext context)
    {
        try
        {
            var keycloakResult = await keycloakService.SetEmailVerified(context.KeycloakUser!.Id, true);
            if ((int)keycloakResult.StatusCode >= 200 && (int)keycloakResult.StatusCode < 300)
            {
                logger.LogInformation("Successfully updated email verification status in Keycloak for user: {UserId}", context.KeycloakUser.Id);
                return Result.Success<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>(
                    new ValidateUserAccountResponse(true, "Email address verified successfully. Your account is now active."));
            }
            else
            {
                logger.LogError("Failed to update email verification status in Keycloak for user: {UserId}, StatusCode: {StatusCode}",
                    context.KeycloakUser.Id, keycloakResult.StatusCode);
                return CreateValidationError("KeycloakUpdateFailed", "Failed to update user verification status");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating Keycloak user for email: {Email}", context.Customer!.Email);
            return CreateValidationError("KeycloakError", "Failed to update user verification status", true);
        }
    }

    public async Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req) =>
        await Result.Success(new ValidateUserAccountContext(req))
            .Bind(ValidateAccountRequest)
            .Bind(GetCustomerByEmailToken)
            .Bind(GetKeycloakUser)
            .Bind(VerifyEmailToken)
            .Bind(UpdateKeycloakEmailVerification);
}
