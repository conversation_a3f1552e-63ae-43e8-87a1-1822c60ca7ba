using commercetools.Sdk.Api.Models.CustomerGroups;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Stores;
using FS.Keycloak.RestApiClient.Model;
using IT.Microservices.AuthenticationApi.Login;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using System.Text.Json.Nodes;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IT.Microservices.AuthenticationApi.CreateUser;

public interface ICreateUserUseCase
{
    public Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req);
    public Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req);
}

public class CreateUserUseCase(ILogger<CreateUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : ICreateUserUseCase
{
    private readonly string? _storeKey = conf?.GetSection("Client:StoreProjectionKey").Value;

    public async Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req)
    {

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.Password) || string.IsNullOrWhiteSpace(req.RedirectUrl))
            return new CreateUserFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };

        string errorContent = string.Empty;
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new CreateUserFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        if (user is not null)
        {
            errorContent = $"User with email {req.Email} already exists in Keycloak";
            logger.LogError(errorContent);
            return new CreateUserFailedResponse { error = "UserAlreadyExist" , error_description = errorContent };
        }

        // right now no validation on the password on our side
        // we create the user with a EmailVerified to false and add an action to verify the email
        var keycloakRes = await keycloakService.CreateUser(new UserRepresentation
        {
            Enabled = true,
            EmailVerified = false,
            Username = req.Email,
            Email = req.Email,
            Credentials = [ new CredentialRepresentation { Type = "password", Temporary = false, Value = req.Password } ],
            RequiredActions = [ "VERIFY_EMAIL"]
        });

        if (!keycloakRes.StatusCode.IsSuccessStatusCode())
        {
            errorContent = $"Error while creating the user into Keycloak for email : {req.Email} with the error : {keycloakRes.ErrorText}";
            logger.LogError(errorContent);
            return new CreateUserFailedResponse { error = "CreationInKeycloakFailed", error_description = errorContent };
        }

        // at this step we have created the customer in Keycloak, now we need to create/update in CommerceTools and generate email token

        // first try to verify if the user exist in CT
        var ctUserExists = await custService.GetByEmail(req.Email);

        if (ctUserExists is null)
        {
            var ctUserDraft = new CustomerDraft
            {
                Email = req.Email,
                //FirstName = req.FirstName,
                //LastName = req.LastName,
                //Title = req.Title,
                Addresses = [],
                IsEmailVerified = false, // we set this to false as the user has to verify his email first
                AuthenticationMode = IAuthenticationMode.ExternalAuth,
                Stores = [new StoreResourceIdentifier() { Key = _storeKey }],
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey },
                CustomerGroupAssignments = [new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } }],

            };

            var ctRes = await custService.CreateCustomer(ctUserDraft);

            if (ctRes is null)
            {
                errorContent = $"Error while creating the user with payload draft : {ctUserDraft.Serialize(Serializer.SerializerType.CommerceTools,serializerService)} in CT from request into CT for email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "CreationInCTFailed", error_description = errorContent };
            }

            // Generate email token for the newly created customer
            var emailTokenResult = await custService.CreateEmailToken(ctRes);
            if (emailTokenResult == null || string.IsNullOrEmpty(emailTokenResult.Value))
            {
                errorContent = $"Error while generating email token for customer : {ctRes.Id} with email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "EmailTokenGenerationFailed", error_description = errorContent };
            }

            // Return redirect URL with the email token for testing
            var redirectUrlNew = $"{req.RedirectUrl}?code={emailTokenResult.Value}";
            logger.LogInformation("User created successfully. Email token generated for customer: {customerId}, redirect URL: {redirectUrl}", ctRes.Id, redirectUrlNew);

            return new CreateUserResponse(true, redirectUrlNew);
        }
        else // case where the user already exist in CT
        {
            logger.LogWarning("User with data from CT : {payload} with email {email} already exists in CT, update process to change customerGroups", ctUserExists.Serialize(Serializer.SerializerType.CommerceTools,serializerService), req.Email);

            var customerUpdateActions = new List<ICustomerUpdateAction>();

            // if no stores are assigned to the user, we add the store of the current country (we authorize only 1 country by account right now)
            if (ctUserExists.Stores is null || ctUserExists.Stores.Count == 0)
            {
                logger.LogWarning("User with email {email} already exists in CT but has no stores, adding store {storeKey}", req.Email, _storeKey);
                customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
            }
            else // the use already has stores assigned, we can check if the store is already assigned
            {
                if(ctUserExists.Stores.All(s => s.Key != _storeKey))
                {
                    logger.LogWarning("User with email {email} already exists in CT but does not have store {storeKey} current stores : {stores} , adding it", req.Email, _storeKey , ctUserExists.Stores?.Serialize(Serializer.SerializerType.CommerceTools,serializerService));
                    customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
                }
            }

            // if the user is not in the registered customer group, we add it
            if (ctUserExists.CustomerGroupAssignments is null || ctUserExists.CustomerGroupAssignments.All(cga => cga.CustomerGroup?.Obj?.Key != CustomerCommonValues.RegisteredCustomerGroupKey))
            {
                logger.LogWarning("User with email {email} already exists in CT but does not have customer group 'registered', adding it", req.Email);
                customerUpdateActions.Add(new CustomerAddCustomerGroupAssignmentAction { CustomerGroupAssignment = new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } } });
            }

            // if the user is in the anonymous customer group, we remove it
            if (ctUserExists.CustomerGroupAssignments is not null && ctUserExists.CustomerGroupAssignments.Any(cga => cga.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey))
            {
                logger.LogWarning("User with email {email} already exists in CT but has customer group 'anonymous', removing it", req.Email);
                customerUpdateActions.Add(new CustomerRemoveCustomerGroupAssignmentAction { CustomerGroup =  new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.AnonymousCustomerGroupKey } });
            }

            if(ctUserExists.CustomerGroup == null)
            {
                logger.LogWarning("User with email {email} already exists in CT but has no customer group, set it with 'registered' group", req.Email);
                customerUpdateActions.Add(new CustomerSetCustomerGroupAction { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } });
            }
            else
            {
                // also verify the classical customer group property and replace it if needed (if it's anonymous)
                if (ctUserExists.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey)
                {
                    logger.LogWarning("User with email {email} already exists in CT but has customer group '{customerGroup}', replacing it with 'registered'", req.Email, ctUserExists.CustomerGroup?.Obj?.Key);
                    customerUpdateActions.Add(new CustomerSetCustomerGroupAction { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } });
                }
            }

            if (customerUpdateActions.Count == 0)
            { 
                logger.LogInformation("User with email {email} already exists in CT and no update actions are needed", req.Email);

                // Generate email token for the existing customer
                var emailTokenResult2 = await custService.CreateEmailToken(ctUserExists);
                if (emailTokenResult2 == null || string.IsNullOrEmpty(emailTokenResult2.Value))
                {
                    errorContent = $"Error while generating email token for customer : {ctUserExists.Id} with email : {req.Email}";
                    logger.LogError(errorContent);
                    return new CreateUserFailedResponse { error = "EmailTokenGenerationFailed", error_description = errorContent };
                }

                // Return redirect URL with the email token for testing
                var redirectUrl2 = $"{req.RedirectUrl}?code={emailTokenResult2.Value}";
                logger.LogInformation("Email token generated for existing customer: {customerId}, redirect URL: {redirectUrl}", ctUserExists.Id, redirectUrl2);

                return new CreateUserResponse(true, redirectUrl2);
            }

            var updatedCust = await custService.UpdateCustomer(ctUserExists,customerUpdateActions);

            // update failed
            if (updatedCust == null)
            {
                errorContent = $"Error while updating the user : {ctUserExists.Serialize(Serializer.SerializerType.CommerceTools , serializerService)} with payload actions update : {customerUpdateActions.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "UpdateInCTFailed", error_description = errorContent };
            }

            // Generate email token for the updated customer
            var emailTokenResult3 = await custService.CreateEmailToken(updatedCust);
            if (emailTokenResult3 == null || string.IsNullOrEmpty(emailTokenResult3.Value))
            {
                errorContent = $"Error while generating email token for customer : {updatedCust.Id} with email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "EmailTokenGenerationFailed", error_description = errorContent };
            }

            // Return redirect URL with the email token for testing
            var redirectUrl3 = $"{req.RedirectUrl}?code={emailTokenResult3.Value}";
            logger.LogInformation("User updated successfully. Email token generated for customer: {customerId}, redirect URL: {redirectUrl}", updatedCust.Id, redirectUrl3);

            return new CreateUserResponse(true, redirectUrl3);
        }

    }

    public async Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.EmailToken))
            return new ValidateUserAccountFailedResponse("InvalidRequestData", "Email token is required");

        try
        {
            // step 0 : Check if the user exists in CommerceTools and in Keycloak

            var customer = await custService.GetCustomerByEmailToken(req.EmailToken);
            if (customer == null)
            {
                logger.LogWarning("Get User from Email token failed for token: {emailToken}", req.EmailToken);
                return new ValidateUserAccountFailedResponse("GetUserInCTFromEmailTokenFailed", "Impossible to find the user in CommerceTools with this EmailToken");
            }

            var keycloakUser = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(customer.Email);
            if (keycloakUser == null)
            {
                logger.LogError("Keycloak user not found for email: {email}", customer.Email);
                return new ValidateUserAccountFailedResponse("UserNotFoundInKeycloak", "User not found in authentication system");
            }

            // Step 1: Verify the email token with CommerceTools and get back the customer (not email verified yet)
            var customerVerifiedInCt = await custService.VerifyEmailToken(req.EmailToken);
            if (customerVerifiedInCt == null)
            {
                logger.LogWarning("Email token verification failed for token: {emailToken}", req.EmailToken);
                return new ValidateUserAccountFailedResponse("InvalidToken", "The email verification token is invalid or has expired");
            }

            logger.LogInformation("Email token verified successfully for customer: {customerId}", customer.Id);

            // Step 2: Update Keycloak user to set EmailVerified = true and remove VERIFY_EMAIL action
            try
            {
                // Set email as verified in Keycloak
                var keycloakResult = await keycloakService.SetEmailVerified(keycloakUser.Id, true);
                if ((int)keycloakResult.StatusCode >= 200 && (int)keycloakResult.StatusCode < 300)
                {
                    logger.LogInformation("Successfully updated email verification status in Keycloak for user: {userId}", keycloakUser.Id);
                }
                else
                {
                    logger.LogError("Failed to update email verification status in Keycloak for user: {userId}, StatusCode: {statusCode}",
                        keycloakUser.Id, keycloakResult.StatusCode);
                    return new ValidateUserAccountFailedResponse("KeycloakUpdateFailed", "Failed to update user verification status");
                }
            }
            catch (Exception keycloakEx)
            {
                logger.LogError(keycloakEx, "Error updating Keycloak user for email: {email}", customer.Email);
                return new ValidateUserAccountFailedResponse("KeycloakError", "Failed to update user verification status");
            }

            return new ValidateUserAccountResponse(true, "Email address verified successfully. Your account is now active.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating user account with token: {emailToken}", req.EmailToken);
            return new ValidateUserAccountFailedResponse("ValidationError", "An error occurred while validating your account. Please try again.");
        }
    }
}
