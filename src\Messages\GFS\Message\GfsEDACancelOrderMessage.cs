﻿using ITF.SharedModels.Group.Enums.Gfs;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDACancelOrderMessage
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Order Cancellation Requested\"";

        [JsonProperty("Order Cancellation Requested")]
        public GfsEDACancelOrderMessagePayload CancelOrderMessage { get; set; } = new();
    }

    public class GfsEDACancelOrderMessagePayload
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsCancelOrderMessageReason Reason { get; set; }
        public string Text { get; set; } = default!;
        public bool ClosingInfo { get; set; }
        public int BackchargeToUnitAmount { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }
}
