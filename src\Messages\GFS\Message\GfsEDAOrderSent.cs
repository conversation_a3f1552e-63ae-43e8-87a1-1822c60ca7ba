﻿using ITF.SharedModels.Group.Enums.Gfs;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDAOrderSent
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Order Sent\"";

        [JsonProperty("Order Sent")]
        public GfsEDAOrderSentPayload OrderSent { get; set; } = new();
    }

    public class GfsEDAOrderSentPayload
    {
        public DateTime DeliveryDate { get; set; }
        public string SendingUnitOrderNumber { get; set; } = default!;
        public string ExecutingUnitOrderNumber { get; set; } = default!;
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOrderType OrderType { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOrderState OrderState { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsOccasion Occasion { get; set; }
        public string SenderCompany { get; set; } = default!;
        public string SenderName { get; set; } = default!;
        public string SenderAddress { get; set; } = default!;
        public string SenderCity { get; set; } = default!;
        public string SenderRegion { get; set; } = default!;
        public string SenderPostalCode { get; set; } = default!;
        public string SenderCountryCode { get; set; } = default!;
        public string SenderPhoneHome { get; set; } = default!;
        public string SenderPhoneWork { get; set; } = default!;
        public string SenderPhoneMobile { get; set; } = default!;
        public string SenderFax { get; set; } = default!;
        public string SenderEmail1 { get; set; } = default!;
        public string SenderEmail2 { get; set; } = default!;
        public string RecipientTitle { get; set; } = default!;
        public string RecipientName { get; set; } = default!;
        public string RecipientAddress { get; set; } = default!;
        public string RecipientCity { get; set; } = default!;
        public string RecipientRegion { get; set; } = default!;
        public string RecipientPostalCode { get; set; } = default!;
        public string RecipientCountryCode { get; set; } = default!;
        public string RecipientPhoneHome { get; set; } = default!;
        public string RecipientPhoneWork { get; set; } = default!;
        public string RecipientPhoneMobile { get; set; } = default!;
        public string RecipientFax { get; set; } = default!;
        public string RecipientEmail { get; set; } = default!;
        public double TotalOrderValue { get; set; }
        public string OrderInstructions { get; set; } = default!;
        public string SendingMemberCode { get; set; } = default!;
        public string SendingLocation { get; set; } = default!;
        public string SendingName { get; set; } = default!;
        public string SendingMemberOrderNumber { get; set; } = default!;
        public string FillingMemberCode { get; set; } = default!;
        public string FillingName { get; set; } = default!;
        public string FillingLocation { get; set; } = default!;
        public object ClearingPeriod { get; set; } = default!;
        public int ExtraDeliveryFee { get; set; }
        public DateTime? DeliveredDate { get; set; }
        public List<GfsOrderItem> OrderItems { get; set; } = new();
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }

    public class GfsOrderItem
    {
        public double Price { get; set; }
        public int Quantity { get; set; }
        public string IntercatCode { get; set; } = default!;
        public string SecondChoice { get; set; } = default!;
        public string SKU { get; set; } = default!;
        public string CardMessage { get; set; } = default!;
        public string Description { get; set; } = default!;
    }
}
