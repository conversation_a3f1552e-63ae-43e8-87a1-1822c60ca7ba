namespace IT.Microservices.AuthenticationApi.CreateUser;

public record CreateUserRequest(string Email, string Password , string RedirectUrl);
public record CreateUserResponse(bool IsSucess, string RedirectUrl);
public record CreateUserFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);


public record ValidateUserAccountRequest(string EmailToken);
public record ValidateUserAccountResponse(bool IsSuccess, string Message);
public record ValidateUserAccountFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);

public class CreateUserController(ILogger<CreateUserController> logger, ICreateUserUseCase createUserUseCase) : BaseController
{
    [SwaggerOperation(
    Summary = "Create User api endpoint",
    Description = "Allow an interflora customer to Create an Account through our group octopus api",
    OperationId = "CreateUser")]
    [SwaggerResponse(200, "the response is sucess", typeof(CreateUserRequest))]
    [SwaggerResponse(200, "the response has an error", typeof(Error))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> CreateUserAsync([FromBody] CreateUserRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request create user received : {req}", req.Serialize());
        var res = await createUserUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
        Summary = "Validate User Account api endpoint",
        Description = "Allow an interflora customer to validate their email address through a token received via email",
        OperationId = "ValidateUserAccount")]
    [SwaggerResponse(200, "the response is sucess", typeof(ValidateUserAccountResponse))]
    [SwaggerResponse(200, "the response has an error", typeof(Error))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> ValidateUserAccountAsync([FromBody] ValidateUserAccountRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        var res = await createUserUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
