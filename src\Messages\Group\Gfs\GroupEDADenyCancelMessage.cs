﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDADenyCancelMessage : BaseMessage<GroupEDADenyCancelMessagePayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDADenyCancelMessage()
                {
                    this.Payload = new GroupEDADenyCancelMessagePayload();
                }

                public static implicit operator GroupEDADenyCancelMessage(GfsEDADenyCancelMessage v)
                {
                    GroupEDADenyCancelMessage msg = new();
                    msg.Payload = new();
                    msg.Payload.Reason = v.DenyCancelMessage.Reason;
                    msg.Payload.Text = v.DenyCancelMessage.Text;
                    msg.Payload.Id = v.DenyCancelMessage.Id;
                    msg.Payload.GFSgateNumber = v.DenyCancelMessage.GFSgateNumber;
                    msg.Payload.MessageStatus = v.DenyCancelMessage.MessageStatus;
                    msg.Payload.MessageType = v.DenyCancelMessage.MessageType;
                    msg.Payload.CreatedDate = v.DenyCancelMessage.CreatedDate;
                    msg.Payload.ModifiedDate = v.DenyCancelMessage.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.DenyCancelMessage.ConfirmedDate;
                    msg.Payload.FromUnitID = v.DenyCancelMessage.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.DenyCancelMessage.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.DenyCancelMessage.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.DenyCancelMessage.ToUnitMessageID;
                    msg.Payload.Operator = v.DenyCancelMessage.Operator;
                    msg.Payload.Priority = v.DenyCancelMessage.Priority;
                    msg.Payload.RelatedMessageId = v.DenyCancelMessage.RelatedMessageId;
                    msg.Payload.Read = v.DenyCancelMessage.Read;
                    return msg;
                }
            }
        }
    }


    public class GroupEDADenyCancelMessagePayload : LegacyPayload, IEquatable<GroupEDADenyCancelMessagePayload>
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsDenyReason Reason { get; set; }
        public string Text { get; set; } = default!;
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDADenyCancelMessagePayload parameter)
        {
            return (Reason == parameter.Reason &&
                    Text == parameter.Text &&
                    Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType == parameter.MessageType &&
                    CreatedDate == parameter.CreatedDate &&
                    ModifiedDate == parameter.ModifiedDate &&
                    ConfirmedDate == parameter.ConfirmedDate &&
                    FromUnitID == parameter.FromUnitID &&
                    FromUnitMessageID == parameter.FromUnitMessageID &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDADenyCancelMessagePayload);
        }

        public override int GetHashCode() => new
        {
            Reason,
            Text,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read
        }.GetHashCode();
    }
}
