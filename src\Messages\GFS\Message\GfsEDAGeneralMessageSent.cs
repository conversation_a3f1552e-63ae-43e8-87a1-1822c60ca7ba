﻿using Newtonsoft.Json;

namespace ITF.SharedModels.Messages.GFS.Message
{

    public class GfsEDAGeneralMessageSent
    {
        [JsonIgnore]
        public static readonly string NameType = "\"General Message Sent\"";

        [JsonProperty("General Message Sent")]
        public GfsEDAGeneralMessageSentPayload GeneralMessageSent { get; set; } = new();
    }
    public class GfsEDAGeneralMessageSentPayload
    {
        public bool IsPersonal { get; set; }
        public string Subject { get; set; } = default!;
        public string Text { get; set; } = default!;
        public DateTime? ApplicableFrom { get; set; }
        public DateTime? ApplicableTo { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        public int MessageStatus { get; set; }
        public int MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }
}
